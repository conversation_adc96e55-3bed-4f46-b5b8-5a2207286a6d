package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.Level;
import org.slf4j.LoggerFactory;

@ExtendWith(MockitoExtension.class)
class TransactionBuilderTest {

    @Mock private TaskExecutor taskExecutor;

    @Mock private DistributedAction<String> action1;

    @Mock private DistributedAction<Integer> action2;

    private TransactionOrchestrator orchestrator;
    private TransactionOrchestrator.TransactionBuilder builder;

    @BeforeEach
    void setUp() {
        // Initialize the orchestrator with mocked TaskExecutor
        orchestrator = new TransactionOrchestrator();
        // Inject TaskExecutor manually as we are not using Spring context
        try {
            var field = TransactionOrchestrator.class.getDeclaredField("taskExecutor");
            field.setAccessible(true);
            field.set(orchestrator, taskExecutor);
        } catch (Exception e) {
            fail("Failed to set taskExecutor field: " + e.getMessage());
        }

        // Create a new transaction builder
        builder = orchestrator.newTransaction();
    }

    @Test
    void addStep_shouldAddStepToList() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");

        // Act
        builder.addStep("step1", action1);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
    }

    @Test
    void addConditionalStep_whenConditionTrue_shouldExecuteStep() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        Function<TransactionContext, Boolean> condition = context -> true;

        // Act
        builder.addConditionalStep("conditional_step", action1, condition);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
    }

    @Test
    void addConditionalStep_whenConditionFalse_shouldSkipStep() throws Exception {
        // Arrange
        Function<TransactionContext, Boolean> condition = context -> false;

        // Act
        builder.addConditionalStep("skipped_step", action1, condition);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, never()).execute(any(TransactionContext.class));
    }

    @Test
    void execute_withMultipleSteps_shouldExecuteInOrder() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn(42);

        // Act
        builder.addStep("step1", action1);
        builder.addStep("step2", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
    }

    @Test
    void execute_whenStepFails_shouldReturnFailureResult() throws Exception {
        // Arrange
        Exception testException = new RuntimeException("Test exception");
        when(action1.execute(any())).thenThrow(testException);

        // Act
        builder.addStep("failing_step", action1);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
    }

    @Test
    void execute_whenStepFails_shouldTriggerCompensation() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        Exception testException = new RuntimeException("Test exception");
        when(action2.execute(any())).thenThrow(testException);

        // Act
        builder.addStep("step1", action1);
        builder.addStep("failing_step", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1).compensate();
    }

    @Test
    void execute_withCompensationDisabled_shouldNotCompensate() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        Exception testException = new RuntimeException("Test exception");
        when(action2.execute(any())).thenThrow(testException);

        // Act
        builder.withCompensation(false);
        builder.addStep("step1", action1);
        builder.addStep("failing_step", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1, never()).compensate();
    }

    @Test
    void executeAsync_shouldExecuteInSeparateThread() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("async_result");

        // Setup taskExecutor to immediately run the provided runnable
        doAnswer(
                        invocation -> {
                            Runnable runnable = invocation.getArgument(0);
                            runnable.run();
                            return CompletableFuture.completedFuture(null);
                        })
                .when(taskExecutor)
                .execute(any(Runnable.class));

        // Act
        builder.addStep("async_step", action1);
        CompletableFuture<TransactionResult> future = builder.executeAsync();
        TransactionResult result = future.get(); // Block until completed

        // Assert
        assertTrue(result.success());
        verify(taskExecutor).execute(any(Runnable.class));
    }

    @Test
    void executeStepWithRetry_shouldRetryFailedSteps() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(3, 10, 1.0, 100);
        Exception testException = new RuntimeException("Temporary failure");

        // Make action fail on first attempt, succeed on second attempt
        when(action1.execute(any())).thenThrow(testException).thenReturn("retry_success");

        // Create a custom step with retry policy
        TransactionStep<String> stepWithRetry =
                new TransactionStep.Builder<String>()
                        .name("retry_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act
        builder.addStep(stepWithRetry);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, times(2))
                .execute(any(TransactionContext.class)); // Called twice due to retry
    }

    @Test
    void executeStepWithRetry_whenExceedsMaxAttempts_shouldFail() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(2, 10, 1.0, 100);
        Exception testException = new RuntimeException("Persistent failure");

        // Make action always fail
        when(action1.execute(any())).thenThrow(testException);

        // Create a custom step with retry policy
        TransactionStep<String> stepWithRetry =
                new TransactionStep.Builder<String>()
                        .name("failing_retry_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act
        builder.addStep(stepWithRetry);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1, times(2))
                .execute(any(TransactionContext.class)); // Called twice due to retry
    }

    @Test
    void setContextValue_shouldStoreValueInContext() throws Exception {
        // Arrange
        String key = "test_key";
        String value = "test_value";
        when(action1.execute(any())).thenAnswer(invocation -> {
            TransactionContext context = invocation.getArgument(0);
            return context.get(key);
        });

        // Act
        builder.setContextValue(key, value);
        builder.addStep("context_test_step", action1);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
    }

    @Test
    void execute_withEmptySteps_shouldReturnSuccess() {
        // Act
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
    }

    @Test
    void compensate_whenCompensationFails_shouldContinueWithOtherSteps() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn(42);

        // Make action1 compensation fail, but action2 compensation succeed
        doThrow(new RuntimeException("Compensation failed")).when(action1).compensate();
        doNothing().when(action2).compensate();

        // Make the third step fail to trigger compensation
        DistributedAction<String> failingAction = mock(DistributedAction.class);
        when(failingAction.execute(any())).thenThrow(new RuntimeException("Step failed"));

        // Act
        builder.addStep("step1", action1);
        builder.addStep("step2", action2);
        builder.addStep("failing_step", failingAction);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1).compensate(); // Should be called despite failure
        verify(action2).compensate(); // Should be called after action1 compensation fails
    }

    @Test
    void calculateDelay_shouldRespectMaxDelay() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(5, 100, 2.0, 500); // Max delay of 500ms
        Exception testException = new RuntimeException("Temporary failure");

        // Make action fail multiple times to test delay calculation
        when(action1.execute(any()))
                .thenThrow(testException)
                .thenThrow(testException)
                .thenThrow(testException)
                .thenReturn("success");

        TransactionStep<String> stepWithRetry =
                new TransactionStep.Builder<String>()
                        .name("delay_test_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act
        builder.addStep(stepWithRetry);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, times(4)).execute(any(TransactionContext.class));
    }

    @Test
    void executeStepWithRetry_whenInterrupted_shouldThrowRuntimeException() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(3, 100, 1.0, 1000);
        Exception testException = new RuntimeException("Temporary failure");

        when(action1.execute(any())).thenThrow(testException);

        TransactionStep<String> stepWithRetry =
                new TransactionStep.Builder<String>()
                        .name("interrupt_test_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act & Assert
        builder.addStep(stepWithRetry);

        // Interrupt the current thread before execution to simulate interruption during sleep
        Thread.currentThread().interrupt();

        TransactionResult result = builder.execute();

        // Should fail due to interruption
        assertFalse(result.success());

        // Clear the interrupted status
        Thread.interrupted();
    }

    @Test
    void execute_withLogLevelInfo_shouldLogSkippedSteps() throws Exception {
        // Arrange
        Logger logger = (Logger) LoggerFactory.getLogger(TransactionOrchestrator.class);
        Level originalLevel = logger.getLevel();
        logger.setLevel(Level.INFO);

        try {
            Function<TransactionContext, Boolean> falseCondition = context -> false;

            // Act
            builder.addConditionalStep("skipped_step", action1, falseCondition);
            TransactionResult result = builder.execute();

            // Assert
            assertTrue(result.success());
            verify(action1, never()).execute(any(TransactionContext.class));
        } finally {
            logger.setLevel(originalLevel);
        }
    }

    @Test
    void execute_withLogLevelInfo_shouldLogSuccessfulSteps() throws Exception {
        // Arrange
        Logger logger = (Logger) LoggerFactory.getLogger(TransactionOrchestrator.class);
        Level originalLevel = logger.getLevel();
        logger.setLevel(Level.INFO);

        try {
            when(action1.execute(any())).thenReturn("result1");

            // Act
            builder.addStep("successful_step", action1);
            TransactionResult result = builder.execute();

            // Assert
            assertTrue(result.success());
            verify(action1).execute(any(TransactionContext.class));
        } finally {
            logger.setLevel(originalLevel);
        }
    }

    @Test
    void compensate_withLogLevelInfo_shouldLogCompensationStart() throws Exception {
        // Arrange
        Logger logger = (Logger) LoggerFactory.getLogger(TransactionOrchestrator.class);
        Level originalLevel = logger.getLevel();
        logger.setLevel(Level.INFO);

        try {
            when(action1.execute(any())).thenReturn("result1");

            DistributedAction<String> failingAction = mock(DistributedAction.class);
            when(failingAction.execute(any())).thenThrow(new RuntimeException("Step failed"));

            // Act
            builder.addStep("step1", action1);
            builder.addStep("failing_step", failingAction);
            TransactionResult result = builder.execute();

            // Assert
            assertFalse(result.success());
            verify(action1).compensate();
        } finally {
            logger.setLevel(originalLevel);
        }
    }

    @Test
    void compensate_withLogLevelInfo_shouldLogSuccessfulCompensation() throws Exception {
        // Arrange
        Logger logger = (Logger) LoggerFactory.getLogger(TransactionOrchestrator.class);
        Level originalLevel = logger.getLevel();
        logger.setLevel(Level.INFO);

        try {
            when(action1.execute(any())).thenReturn("result1");
            doNothing().when(action1).compensate();

            DistributedAction<String> failingAction = mock(DistributedAction.class);
            when(failingAction.execute(any())).thenThrow(new RuntimeException("Step failed"));

            // Act
            builder.addStep("step1", action1);
            builder.addStep("failing_step", failingAction);
            TransactionResult result = builder.execute();

            // Assert
            assertFalse(result.success());
            verify(action1).compensate();
        } finally {
            logger.setLevel(originalLevel);
        }
    }

    @Test
    void compensate_withLogLevelInfo_shouldLogFailedCompensation() throws Exception {
        // Arrange
        Logger logger = (Logger) LoggerFactory.getLogger(TransactionOrchestrator.class);
        Level originalLevel = logger.getLevel();
        logger.setLevel(Level.INFO);

        try {
            when(action1.execute(any())).thenReturn("result1");
            doThrow(new RuntimeException("Compensation failed")).when(action1).compensate();

            DistributedAction<String> failingAction = mock(DistributedAction.class);
            when(failingAction.execute(any())).thenThrow(new RuntimeException("Step failed"));

            // Act
            builder.addStep("step1", action1);
            builder.addStep("failing_step", failingAction);
            TransactionResult result = builder.execute();

            // Assert
            assertFalse(result.success());
            verify(action1).compensate();
        } finally {
            logger.setLevel(originalLevel);
        }
    }

    @Test
    void executeStepWithRetry_shouldCallErrorHandler() throws Exception {
        // Arrange
        Consumer<Exception> errorHandler = mock(Consumer.class);
        Exception testException = new RuntimeException("Test exception");

        when(action1.execute(any())).thenThrow(testException).thenReturn("success");

        TransactionStep<String> stepWithErrorHandler =
                new TransactionStep.Builder<String>()
                        .name("error_handler_step")
                        .action(action1)
                        .retryPolicy(new RetryPolicy(2, 10, 1.0, 100))
                        .onError(errorHandler)
                        .build();

        // Act
        builder.addStep(stepWithErrorHandler);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(errorHandler).accept(testException);
        verify(action1, times(2)).execute(any(TransactionContext.class));
    }

    @Test
    void execute_shouldStoreStepResultsInContext() throws Exception {
        // Arrange
        String expectedResult = "step_result";
        when(action1.execute(any())).thenReturn(expectedResult);

        // Create a second action that reads the first step's result
        when(action2.execute(any())).thenAnswer(invocation -> {
            TransactionContext context = invocation.getArgument(0);
            return context.get("step1_result");
        });

        // Act
        builder.addStep("step1", action1);
        builder.addStep("step2", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
        verify(action2).execute(any(TransactionContext.class));
    }

    @Test
    void execute_shouldTrackExecutedStepsInContext() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn(42);

        // Create a third action that checks executed steps
        DistributedAction<String> checkingAction = mock(DistributedAction.class);
        when(checkingAction.execute(any())).thenAnswer(invocation -> {
            TransactionContext context = invocation.getArgument(0);
            return String.valueOf(context.getExecutedSteps().size());
        });

        // Act
        builder.addStep("step1", action1);
        builder.addStep("step2", action2);
        builder.addStep("checking_step", checkingAction);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
        verify(action2).execute(any(TransactionContext.class));
        verify(checkingAction).execute(any(TransactionContext.class));
    }

    @Test
    void calculateDelay_withExponentialBackoff_shouldIncreaseDelay() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(4, 10, 2.0, 1000);
        Exception testException = new RuntimeException("Temporary failure");

        // Make action fail 3 times then succeed
        when(action1.execute(any()))
                .thenThrow(testException)
                .thenThrow(testException)
                .thenThrow(testException)
                .thenReturn("success");

        TransactionStep<String> stepWithBackoff =
                new TransactionStep.Builder<String>()
                        .name("backoff_test_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act
        builder.addStep(stepWithBackoff);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, times(4)).execute(any(TransactionContext.class));
    }

    @Test
    void execute_withMixedConditionalAndRegularSteps_shouldExecuteCorrectly() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn(42);

        Function<TransactionContext, Boolean> trueCondition = context -> true;
        Function<TransactionContext, Boolean> falseCondition = context -> false;

        // Act
        builder.addStep("regular_step", action1);
        builder.addConditionalStep("conditional_true", action2, trueCondition);
        builder.addConditionalStep("conditional_false", action1, falseCondition);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, times(1)).execute(any(TransactionContext.class)); // Only regular step
        verify(action2, times(1)).execute(any(TransactionContext.class)); // Only true conditional
    }

    @Test
    void execute_withContextDependentCondition_shouldEvaluateCorrectly() throws Exception {
        // Arrange
        String contextKey = "enable_step";
        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn(42);

        Function<TransactionContext, Boolean> contextCondition =
            context -> Boolean.TRUE.equals(context.get(contextKey));

        // Act
        builder.setContextValue(contextKey, true);
        builder.addStep("setup_step", action1);
        builder.addConditionalStep("context_dependent_step", action2, contextCondition);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
        verify(action2).execute(any(TransactionContext.class));
    }

    @Test
    void execute_withDebugLogging_shouldLogRetryAttempts() throws Exception {
        // Arrange
        Logger logger = (Logger) LoggerFactory.getLogger(TransactionOrchestrator.class);
        Level originalLevel = logger.getLevel();
        logger.setLevel(Level.DEBUG);

        try {
            RetryPolicy retryPolicy = new RetryPolicy(2, 10, 1.0, 100);
            Exception testException = new RuntimeException("Temporary failure");

            when(action1.execute(any())).thenThrow(testException).thenReturn("success");

            TransactionStep<String> stepWithRetry =
                    new TransactionStep.Builder<String>()
                            .name("debug_retry_step")
                            .action(action1)
                            .retryPolicy(retryPolicy)
                            .build();

            // Act
            builder.addStep(stepWithRetry);
            TransactionResult result = builder.execute();

            // Assert
            assertTrue(result.success());
            verify(action1, times(2)).execute(any(TransactionContext.class));
        } finally {
            logger.setLevel(originalLevel);
        }
    }
}
